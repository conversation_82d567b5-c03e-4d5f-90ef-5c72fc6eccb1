{"Debug": {"Enable Debugging to Console": false}, "General Settings": {"Admin messages color": "#c0c0c0", "AuthLevel needed for console commands": 2, "Broadcast messages color": "#ffffff", "Chat/Message prefix": "[Pars Drop Services]", "Prefix color": "#00ffff", "Prefix format": "<size=12><color={0}>{1}</color>: ", "GUI Announce command": "announce", "Show message to admin after command usage": true, "Time for active smoke of SupplySignal (default is 210)": 210.0, "Lock DirectDrop to be looted only by target player": true, "Lock SignalDrop to be looted only by target player": false, "Unlock crates only after player stopped looting": false}, "Airdrop & General Plane Settings": {"Default radius for location based massdrop": 100.0, "Delay between Massdrop plane spawns": 6.0, "Massdrop default plane amount": 4, "Multiplier for (plane height * highest point on Map); Default 1.0": 1.0, "Multiplier for overall flight distance; lower means faster at map": 2.0, "Disable SupplySignal randomization": false, "Visual & Sound Effects Settings": {"Use Explosion Sound Effect when hits ground position": true, "Deploy Smoke on drop as it falls": false, "Deploy with Audio Alarm on drop": true, "Deploy with Audio Alarms on drop only during the night": true, "Deploy with Spinning Siren Light on drop": true, "Deploy with Spinning Siren Light on drop only during the night": true, "Enable Rocket Signal upon Supply Drop Landing": false, "Signal rocket speed": 10.0, "Signal rocket explosion timer": 10.0}, "Space Delivery Drop Settings": {"Incoming Space Delivery Supply Drop velocity": 70.0, "Parachute deploy distance from ground": 50.0}}, "Allow Damage Airdrop Settings": {"Players can shoot down the drop": true, "Players can shoot down the drop - needed hits": 3, "Set Angular Drag for drop": 0.1, "Set Drag for drop (drop resistance)": 0.3, "Set drop allow to explode on impact": true, "Set drop chance exploding on impact (x out of 100)": 25, "Set Mass weight for drop": 0.75}, "Notification Settings": {"Maximum distance in meters to get notified about landed Drop": 1000.0, "Maximum distance in meters to get notified about looted Drop": 1000.0, "Notify a player about incoming Drop to his location": false, "Notify a player about spawned Drop at his location": false, "Notify a player about Space Delivery Drop at his location": true, "Notify admins per chat about player who has thrown SupplySignal": false, "Notify console at Drop by SupplySignal": true, "Notify console at timed-regular Drop": true, "Notify console when a Drop is being looted": true, "Notify console when Drop is landed": false, "Notify console when Drop is spawned": false, "Notify console when Drop landed/spawned only at the first": true, "Notify players at custom/event Drop": true, "Notify players at custom/event Drop including Coords": false, "Notify players at custom/event Drop including Grid Area": false, "Notify players at Drop by SupplySignal": false, "Notify players at Drop by SupplySignal including Coords": false, "Notify players at Drop by SupplySignal including Grid Area": false, "Notify players at Massdrop": true, "Notify players at Random/Timed Drop": true, "Notify players at Random/Timed Drop including Coords": false, "Notify players at Random/Timed Drop including Grid Area": false, "Notify players when a Drop is being looted": false, "Notify players when a Drop is being looted including coords": false, "Notify players when a Drop is being looted including Grid Area": false, "Notify players when Drop is landed about distance": false, "Notify Players who has thrown a SupplySignal": false, "Notify Players who has thrown a SupplySignal including coords": false, "Notify Players who has thrown a SupplySignal including Grid Area": false, "Use GUI Announcements for any Drop notification": false}, "SimpleUI": {"SimpleUI_Enable": true, "SimpleUI_FontSize": 25, "SimpleUI_HideTimer": 10.0, "SimpleUI_NoticeColor": "1 1 1 0.9", "SimpleUI_ShadowColor": "0.1 0.1 0.1 0.5", "SimpleUI_Top": 0.05, "SimpleUI_Left": 0.1, "SimpleUI_MaxWidth": 0.8, "SimpleUI_MaxHeight": 0.1}, "Timer - Random Event": {"Use Airdrop timer": true, "Used Airdrop timer command": "random", "Minimum players for timed Drop": 2, "Minimum minutes for random timer delay": 70, "Maximum minutes for random timer delay": 140, "Reset Timer after manual random drop": false, "Remove builtIn Airdrop": true, "Cleanup old Drops at serverstart": true}, "Timers- System Timers": {"Log to console": true, "Minimum players for running Timers": 0, "Use RealTime": false, "RealTime": {"16:00": "ad.massdrop 3", "18:00": "ad.toplayer *"}, "Use ServerTime": false, "ServerTime": {"6": "ad.massdrop 3", "18": "ad.massdropto 0 0 5 100"}}, "Drop Settings": {"DropDefault": {"minCrates": 1, "maxCrates": 1, "cratesGap": 50, "despawnMinutes": 10, "crateAirResistance": 2.0, "useCustomLootTable": false, "CustomLootListName": "regular", "planeSpeed": 100, "additionalheight": 0}, "DropTypes": {"regular": {"minCrates": 1, "maxCrates": 1, "cratesGap": 50, "despawnMinutes": 10, "crateAirResistance": 2.0, "useCustomLootTable": false, "CustomLootListName": "regular", "planeSpeed": 100, "additionalheight": 0}, "supplysignal": {"minCrates": 1, "maxCrates": 1, "cratesGap": 50, "despawnMinutes": 10, "crateAirResistance": 2.0, "useCustomLootTable": false, "CustomLootListName": "supplysignal", "planeSpeed": 110, "additionalheight": 0}, "massdrop": {"minCrates": 1, "maxCrates": 1, "cratesGap": 70, "despawnMinutes": 10, "crateAirResistance": 2.0, "useCustomLootTable": false, "CustomLootListName": "massdrop", "planeSpeed": 100, "additionalheight": 0}, "dropdirect": {"minCrates": 1, "maxCrates": 1, "cratesGap": 70, "despawnMinutes": 10, "crateAirResistance": 2.0, "useCustomLootTable": false, "CustomLootListName": "dropdirect", "planeSpeed": 105, "additionalheight": 0}, "custom_event": {"minCrates": 1, "maxCrates": 1, "cratesGap": 50, "despawnMinutes": 15, "crateAirResistance": 2.0, "useCustomLootTable": false, "CustomLootListName": "custom_event", "planeSpeed": 105, "additionalheight": 0, "notificationInfo": "Custom Stuff"}}}, "Custom Loot Items": {"DropTypes": [{"DropType Name": "regular", "Minimum amount of items to spawn": 2, "Maximum amount of items to spawn": 6, "Custom Loot Contents": [{"Shortname": "metalspring", "Minimum amount of item": 2, "Maximum amount of item": 6, "Skin ID": 0, "Display Name": null}, {"Shortname": "mining.quarry", "Minimum amount of item": 1, "Maximum amount of item": 2, "Skin ID": 0, "Display Name": null}]}, {"DropType Name": "massdrop", "Minimum amount of items to spawn": 2, "Maximum amount of items to spawn": 6, "Custom Loot Contents": [{"Shortname": "metalpipe", "Minimum amount of item": 1, "Maximum amount of item": 2, "Skin ID": 0, "Display Name": null}, {"Shortname": "pistol.revolver", "Minimum amount of item": 1, "Maximum amount of item": 1, "Skin ID": 0, "Display Name": null}]}, {"DropType Name": "dropdirect", "Minimum amount of items to spawn": 2, "Maximum amount of items to spawn": 6, "Custom Loot Contents": [{"Shortname": "syringe.medical", "Minimum amount of item": 2, "Maximum amount of item": 6, "Skin ID": 0, "Display Name": null}, {"Shortname": "largemedkit", "Minimum amount of item": 1, "Maximum amount of item": 2, "Skin ID": 0, "Display Name": null}]}, {"DropType Name": "supplysignal", "Minimum amount of items to spawn": 2, "Maximum amount of items to spawn": 6, "Custom Loot Contents": [{"Shortname": "metal.fragments", "Minimum amount of item": 1, "Maximum amount of item": 4, "Skin ID": 0, "Display Name": null}, {"Shortname": "metal.facemask", "Minimum amount of item": 1, "Maximum amount of item": 1, "Skin ID": 0, "Display Name": null}]}, {"DropType Name": "custom_event", "Minimum amount of items to spawn": 2, "Maximum amount of items to spawn": 6, "Custom Loot Contents": [{"Shortname": "fish.cooked", "Minimum amount of item": 2, "Maximum amount of item": 6, "Skin ID": 0, "Display Name": null}, {"Shortname": "metal.plate.torso", "Minimum amount of item": 1, "Maximum amount of item": 1, "Skin ID": 0, "Display Name": null}]}]}, "Version": {"Major": 3, "Minor": 2, "Patch": 5}}