{"Kits": [{"Name": "<PERSON><PERSON><PERSON>", "Display Name": "Base", "Color": "", "Permission": "Kits.default", "Description": "For First Build Base", "Image": "https://static.wikia.nocookie.net/play-rust/images/b/ba/Building_Plan_icon.png/revision/latest?cb=20151106061847", "Hide": false, "ShowInfo": true, "Amount": 3, "Cooldown": 64800.0, "Wipe Block": 18000.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 6000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 4000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.refined", "DisplayName": "", "Amount": 65, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 2000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lowgradefuel", "DisplayName": "", "Amount": 50, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "cupboard.tool.retro", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 1.0, "Chance": 100, "Position": 18, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lock.code", "DisplayName": "", "Amount": 3, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 20, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "storage_barrel_c", "DisplayName": "", "Amount": 2, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 13, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "storage_barrel_b", "DisplayName": "", "Amount": 2, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 14, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "furnace", "DisplayName": "Ghostly Flame", "Amount": 2, "Blueprint": 0, "SkinID": 1539005624, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 12, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "door.hinged.wood", "DisplayName": "Hell-o-ween Wooden Door", "Amount": 3, "Blueprint": 0, "SkinID": 1539681696, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 19, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "building.planner", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hammer", "DisplayName": "<PERSON><PERSON><PERSON><PERSON>", "Amount": 1, "Blueprint": 0, "SkinID": 2363269093, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "sleepingbag", "DisplayName": "Danger Zone Sleeping Bag", "Amount": 1, "Blueprint": 0, "SkinID": 3371748520, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "Streamer", "Display Name": "<PERSON><PERSON>", "Color": "", "Permission": "kits.streamer", "Description": "", "Image": "https://files.facepunch.com/rust/item/axe.salvaged_512.png", "Hide": false, "ShowInfo": true, "Amount": 3, "Cooldown": 43200.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "woodtea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "radiationresisttea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "oretea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "<PERSON><PERSON>a", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "axe.salvaged", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 500.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hammer.salvaged", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 500.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "icepick.salvaged", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 500.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "recource", "Display Name": "Streamer Base", "Color": "", "Permission": "kits.streamer", "Description": "S<PERSON>l Ston,Metal,Wood va...", "Image": "https://s32.picofile.com/file/**********/rust_wood_computer_servers_metal_woods_removebg_preview_1_.png", "Hide": false, "ShowInfo": true, "Amount": 2, "Cooldown": 100000.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 6000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 4000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 5000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 5000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 3000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.refined", "DisplayName": "", "Amount": 100, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 9, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 10, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 11, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "furnace", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 2843657577, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 8, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "furnace", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 2843657577, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 7, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "furnace", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 2843657577, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 6, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wall.frame.garagedoor", "DisplayName": "Geitta Garage Door", "Amount": 1, "Blueprint": 0, "SkinID": 2842922008, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 14, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "door.double.hinged.metal", "DisplayName": "Base Invaders Double Door", "Amount": 1, "Blueprint": 0, "SkinID": 3322894229, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 13, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "door.hinged.metal", "DisplayName": "Lovers Sheet Metal Door", "Amount": 1, "Blueprint": 0, "SkinID": 2383545397, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 12, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "bed", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 16, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lock.key", "DisplayName": "", "Amount": 3, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 17, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "cupboard.tool.shockbyte", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 15, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "building.planner", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hammer", "DisplayName": "Vice Hammer", "Amount": 1, "Blueprint": 0, "SkinID": 2060277379, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "tools", "Display Name": "Tools", "Color": "", "Permission": "", "Description": "", "Image": "https://files.facepunch.com/rust/item/pickaxe_512.png", "Hide": false, "ShowInfo": true, "Amount": 7, "Cooldown": 1200.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lumberjack.pickaxe", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 400.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lumberjack.hatchet", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 400.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "divertorch", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 50.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "med", "Display Name": "Med", "Color": "", "Permission": "", "Description": "", "Image": "https://files.facepunch.com/rust/item/bandage_512.png", "Hide": false, "ShowInfo": true, "Amount": 10, "Cooldown": 2000.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "bandage", "DisplayName": "", "Amount": 6, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "pumpkin", "DisplayName": "", "Amount": 3, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "corn", "DisplayName": "", "Amount": 3, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "syringe.medical", "DisplayName": "", "Amount": 2, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "bow", "Display Name": "Bow", "Color": "", "Permission": "", "Description": "", "Image": "https://files.facepunch.com/rust/item/bow.hunting_512.png", "Hide": false, "ShowInfo": true, "Amount": 10, "Cooldown": 3000.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "attire.ninja.suit", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "arrow.wooden", "DisplayName": "", "Amount": 25, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "bow.hunting", "DisplayName": "Comics Bow", "Amount": 1, "Blueprint": 0, "SkinID": 2599664731, "Container": "belt", "Condition": 50.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": {"ammoType": "arrow.wooden", "ammoAmount": 0}, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "spear.cny", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "pvp", "Display Name": "Streamer PVP", "Color": "", "Permission": "kits.streamer", "Description": "<PERSON>olver,<PERSON>gun,Medic va...", "Image": "https://files.facepunch.com/rust/item/rifle.semiauto_512.png", "Hide": false, "ShowInfo": true, "Amount": 3, "Cooldown": 43200.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.rifle", "DisplayName": "", "Amount": 50, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.handmade.shell", "DisplayName": "", "Amount": 20, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hazmatsuit.arcticsuit", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 450.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "syringe.medical", "DisplayName": "", "Amount": 3, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "shotgun.double", "DisplayName": "Heat Double Shotgun", "Amount": 1, "Blueprint": 0, "SkinID": 854987948, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": {"ammoType": "ammo.handmade.shell", "ammoAmount": 0}, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "rifle.semiauto", "DisplayName": "Elite Crate SAR", "Amount": 1, "Blueprint": 0, "SkinID": **********, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": {"ammoType": "ammo.rifle", "ammoAmount": 0}, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "contents", "Display Name": "Vip Base", "Color": "", "Permission": "kits.vip", "Description": "Vip Kit", "Image": "https://files.facepunch.com/rust/item/metal.refined_512.png", "Hide": false, "ShowInfo": true, "Amount": 2, "Cooldown": 86000.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "woodtea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "radiationresisttea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "oretea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "<PERSON><PERSON>a", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "cloth", "DisplayName": "", "Amount": 1000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 7, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lowgradefuel", "DisplayName": "", "Amount": 500, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 9, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.refined", "DisplayName": "", "Amount": 100, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 8, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 3000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 2000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 6, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 6000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "leather", "DisplayName": "", "Amount": 700, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 10, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 12, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 13, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 14, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "pumpkin", "DisplayName": "", "Amount": 5, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 16, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "bed", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 18, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 6000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 17, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 5000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 19, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 5000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 20, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 2000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 21, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lock.key", "DisplayName": "", "Amount": 5, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 11, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wall.frame.garagedoor", "DisplayName": "Ironmouse Garage Door", "Amount": 1, "Blueprint": 0, "SkinID": 3358603556, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 15, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "bone.armor.suit", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 100.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "deer.skull.mask", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 100.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "smallbackpack", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 200.0, "Chance": 100, "Position": 7, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "cupboard.tool.shockbyte", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "building.planner", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hammer", "DisplayName": "Hammer Head Zombie", "Amount": 1, "Blueprint": 0, "SkinID": 2627846183, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "viptools", "Display Name": "Vip <PERSON>ls", "Color": "", "Permission": "kits.vip", "Description": "", "Image": "https://files.facepunch.com/rust/item/axe.salvaged_512.png", "Hide": false, "ShowInfo": true, "Amount": 0, "Cooldown": 43200.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "woodtea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "radiationresisttea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "oretea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "<PERSON><PERSON>a", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "axe.salvaged", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 500.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hammer.salvaged", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 500.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "icepick.salvaged", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 500.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "vippvp", "Display Name": "VipPVP", "Color": "", "Permission": "kits.vip", "Description": "<PERSON>olver,<PERSON>gun,Medic va...", "Image": "https://files.facepunch.com/rust/item/smg.2_512.png", "Hide": false, "ShowInfo": true, "Amount": 3, "Cooldown": 43200.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.pistol", "DisplayName": "", "Amount": 100, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hazmatsuit.nomadsuit", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 450.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "syringe.medical", "DisplayName": "", "Amount": 5, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "smg.2", "DisplayName": "Redemption SMG", "Amount": 1, "Blueprint": 0, "SkinID": **********, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": {"ammoType": "ammo.pistol", "ammoAmount": 0}, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "masterbase", "Display Name": "Master Base", "Color": "", "Permission": "kits.master", "Description": "S<PERSON>l Ston,Metal,Wood va...", "Image": "https://s32.picofile.com/file/**********/rust_wood_computer_servers_metal_woods_removebg_preview_1_.png", "Hide": false, "ShowInfo": true, "Amount": 2, "Cooldown": 100000.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 6000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 6000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "wood", "DisplayName": "", "Amount": 6000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 3000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 3000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 3000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.fragments", "DisplayName": "", "Amount": 3000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 6, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 5000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 7, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 5000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 8, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 5000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 9, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "stones", "DisplayName": "", "Amount": 3000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 10, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "leather", "DisplayName": "", "Amount": 1000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 11, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "leather", "DisplayName": "", "Amount": 1000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 12, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "cloth", "DisplayName": "", "Amount": 2000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 13, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.refined", "DisplayName": "", "Amount": 200, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 14, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 21, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "gunpowder", "DisplayName": "", "Amount": 1000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 17, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "easter.goldegg", "DisplayName": "", "Amount": 2, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 18, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 22, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffin.storage", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 23, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "door.double.hinged.toptier", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 3074139282, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 20, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "door.double.hinged.toptier", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 3074139282, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 19, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lowgradefuel", "DisplayName": "", "Amount": 1000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 15, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hazmatsuittwitch", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 450.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "workbench2", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 1.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "cupboard.tool.shockbyte", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hammer", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 2968108685, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "building.planner", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "masterpvp", "Display Name": "MasterPVP", "Color": "", "Permission": "kits.master", "Description": "<PERSON>olver,<PERSON>gun,Medic va...", "Image": "https://files.facepunch.com/rust/item/smg.mp5_512.png", "Hide": false, "ShowInfo": true, "Amount": 2, "Cooldown": 43200.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.rifle", "DisplayName": "", "Amount": 256, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.pistol", "DisplayName": "", "Amount": 256, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "syringe.medical", "DisplayName": "", "Amount": 5, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 18, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "syringe.medical", "DisplayName": "", "Amount": 5, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 19, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "syringe.medical", "DisplayName": "", "Amount": 5, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 20, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.shotgun", "DisplayName": "", "Amount": 10, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "coffeecan.helmet", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": **********, "Container": "wear", "Condition": 200.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "roadsign.kilt", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": **********, "Container": "wear", "Condition": 150.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "roadsign.jacket", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 2199785536, "Container": "wear", "Condition": 500.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "roadsign.gloves", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 2799639349, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "tshirt.long", "DisplayName": "Creepy Jack", "Amount": 1, "Blueprint": 0, "SkinID": 10106, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "pants", "DisplayName": "Blackout Pants", "Amount": 1, "Blueprint": 0, "SkinID": 2080977144, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "shoes.boots", "DisplayName": "<PERSON>", "Amount": 1, "Blueprint": 0, "SkinID": 10034, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 6, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "largemedkit", "DisplayName": "", "Amount": 5, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "largemedkit", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "smg.mp5", "DisplayName": "Redemption MP5", "Amount": 1, "Blueprint": 0, "SkinID": 3013205963, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": {"ammoType": "ammo.pistol", "ammoAmount": 0}, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "rifle.sks", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": {"ammoType": "ammo.rifle", "ammoAmount": 0}, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "smg.thompson", "DisplayName": "MK-ULTRA Thompson", "Amount": 1, "Blueprint": 0, "SkinID": 3265461713, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": {"ammoType": "ammo.pistol", "ammoAmount": 0}, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "shotgun.double", "DisplayName": "DD Inc DBS", "Amount": 1, "Blueprint": 0, "SkinID": 2118688615, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": {"ammoType": "ammo.handmade.shell", "ammoAmount": 0}, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "mastertools", "Display Name": "Master Tools", "Color": "", "Permission": "kits.master", "Description": "", "Image": "https://files.facepunch.com/rust/item/jackhammer_512.png", "Hide": false, "ShowInfo": true, "Amount": 3, "Cooldown": 43200.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "lowgradefuel", "DisplayName": "", "Amount": 50, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "oretea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "woodtea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "scraptea", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "chainsaw", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "jackhammer", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 300.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "wipe", "Display Name": "wipe", "Color": "", "Permission": "kits.default", "Description": "", "Image": "", "Hide": true, "ShowInfo": false, "Amount": 0, "Cooldown": 0.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.rifle", "DisplayName": "", "Amount": 231, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.rifle", "DisplayName": "", "Amount": 820, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.rifle", "DisplayName": "", "Amount": 1000, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "ammo.rocket.basic", "DisplayName": "", "Amount": 999, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "explosive.timed", "DisplayName": "", "Amount": 990, "Blueprint": 0, "SkinID": 0, "Container": "main", "Condition": 0.0, "Chance": 100, "Position": 6, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "sleepingbag", "DisplayName": "Ludwig Sleeping Bag", "Amount": 1, "Blueprint": 0, "SkinID": 2546247415, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "sleepingbag", "DisplayName": "Ludwig Sleeping Bag", "Amount": 1, "Blueprint": 0, "SkinID": 2546247415, "Container": "main", "Condition": 100.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.plate.torso", "DisplayName": "Raptor Chestplate", "Amount": 1, "Blueprint": 0, "SkinID": 2131557341, "Container": "wear", "Condition": 360.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "roadsign.gloves", "DisplayName": "Berserker Roadsign Gloves", "Amount": 1, "Blueprint": 0, "SkinID": 2833922634, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "roadsign.kilt", "DisplayName": "Junkyard Samurai Pants", "Amount": 1, "Blueprint": 0, "SkinID": 934742835, "Container": "wear", "Condition": 150.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "metal.facemask", "DisplayName": "RustPlatz Facemask", "Amount": 1, "Blueprint": 0, "SkinID": 2779049132, "Container": "wear", "Condition": 320.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "shoes.boots", "DisplayName": "<PERSON>", "Amount": 1, "Blueprint": 0, "SkinID": 10034, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 6, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "pants", "DisplayName": "Tactical Pants", "Amount": 1, "Blueprint": 0, "SkinID": **********, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "hoodie", "DisplayName": "Charitable Rust 2018 Hoodie", "Amount": 1, "Blueprint": 0, "SkinID": **********, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "syringe.medical", "DisplayName": "", "Amount": 95, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 3, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "largemedkit", "DisplayName": "", "Amount": 99, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 5, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "barricade.wood.cover", "DisplayName": "", "Amount": 6, "Blueprint": 0, "SkinID": 0, "Container": "belt", "Condition": 0.0, "Chance": 100, "Position": 4, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "rocket.launcher", "DisplayName": "ROCHA LAUNCHER 4444", "Amount": 1, "Blueprint": 0, "SkinID": **********, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": {"ammoType": "ammo.rocket.basic", "ammoAmount": 0}, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "rifle.ak", "DisplayName": "<PERSON> - AK47", "Amount": 1, "Blueprint": 0, "SkinID": 1886272847, "Container": "belt", "Condition": 130.5, "Chance": 100, "Position": 0, "Image": null, "Weapon": {"ammoType": "ammo.rifle", "ammoAmount": 0}, "Content": [{"ShortName": "weapon.mod.holosight", "Condition": 300.0, "Amount": 1}, {"ShortName": "weapon.mod.flashlight", "Condition": 300.0, "Amount": 1}], "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "rifle.bolt", "DisplayName": "Redemption BAR", "Amount": 1, "Blueprint": 0, "SkinID": 3002533749, "Container": "belt", "Condition": 100.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": {"ammoType": "ammo.rifle", "ammoAmount": 0}, "Content": [{"ShortName": "weapon.mod.small.scope", "Condition": 300.0, "Amount": 1}], "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}, {"Name": "cae1147334fc4a83be19a8308c412e9a", "Display Name": "autocloth", "Color": "#A0A935", "Permission": "kits.streamer", "Description": "", "Image": "", "Hide": true, "ShowInfo": false, "Amount": 0, "Cooldown": 0.0, "Wipe Block": 0.0, "Use Building": false, "Building": null, "Enable sale": false, "Selling price": 0, "Items": [{"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "shirt.tanktop", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 0, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "twitchsunglasses", "DisplayName": "", "Amount": 1, "Blueprint": 0, "SkinID": 0, "Container": "wear", "Condition": 200.0, "Chance": 100, "Position": 2, "Image": null, "Weapon": null, "Content": null, "Text": null}, {"Type": "<PERSON><PERSON>", "Command": "", "ShortName": "pants.shorts", "DisplayName": "Rust Footballer <PERSON><PERSON>", "Amount": 1, "Blueprint": 0, "SkinID": 1412875829, "Container": "wear", "Condition": 0.0, "Chance": 100, "Position": 1, "Image": null, "Weapon": null, "Content": null, "Text": null}], "Use commands on receiving?": false, "Commands on receiving (via '|')": ""}]}